import { getWalletClient } from '@wagmi/core';
import React, { useContext, useState } from 'react';
import { erc20Abi, formatUnits, getContract } from 'viem';
import { WagmiContext } from 'wagmi';

import { IPresale, Membership } from '@/class/interface/presale';
import { PresaleFactory } from '@/class/presale-factory';
import { cutDecimals } from '@/lib/cut-decimals';
import { MembershipsContext } from '@/providers/membership-provider';
import { PresaleContext } from '@/providers/presale-provider';
import { ProjectConfigContext } from '@/providers/project-config-provider';
import { PublicClientContext } from '@/providers/public-client-provider';
import { TokensContext } from '@/providers/tokens-provider';

// Simple validation result interface
interface ValidationResult {
  isValid: boolean;
  reason?: string;
  roundType?: 'TGLP' | 'FCFS' | 'UNKNOWN';
}

/**
 * Validates if membership has claimable tokens
 * @param membership - The membership to validate
 * @returns Validation result
 */
function validateMembership(membership: Membership): ValidationResult {
  const roundType = detectRoundType(membership);

  // Only check if membership has any claimable amount (usage.max > 0)
  if (BigInt(membership.usage.max) === 0n) {
    return {
      isValid: false,
      reason: 'No tokens available to claim back',
      roundType,
    };
  }

  // If usage.max > 0, the membership is valid
  return {
    isValid: true,
    roundType,
  };
}

/**
 * Detects the round type based on membership or round characteristics
 * @param membership - The membership to analyze
 * @returns Round type identifier
 */
function detectRoundType(membership: Membership): 'TGLP' | 'FCFS' | 'UNKNOWN' {
  // Primary detection: Check price patterns
  // TGLP rounds typically have higher prices (350000+ wei)
  // FCFS rounds typically have lower prices (100000000000000000 wei = 0.1 ETH)
  const priceValue = BigInt(membership.price);
  if (priceValue >= 350000n) {
    return 'TGLP';
  } else if (priceValue === 100000000000000000n) {
    return 'FCFS';
  }

  // Secondary detection: Check vesting characteristics
  // FCFS rounds often have very long vesting periods (20736000 periods)
  // TGLP rounds typically have shorter vesting periods (240 periods)
  if (membership.vestingPeriodCount >= 20000000) {
    return 'FCFS';
  } else if (membership.vestingPeriodCount <= 1000) {
    return 'TGLP';
  }

  return 'UNKNOWN';
}





import { Spinner } from '../icons/spinner';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Checkbox } from '../ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { RadioGroup, RadioGroupItem } from '../ui/radio-group';
import { Label } from '../ui/label';

// Old presale contract configuration - now handled by OldMembershipsProvider
// These constants are moved to the provider for better separation of concerns
const OLD_PRESALE_CONTRACT_ADDRESS =
  '0x04aAC06e8f5f2D6f76177819C9fd69736FdbF7f2' as const;
const OLD_PRESALE_VERSION = 'v3' as const;

interface MigrateProps {
  membership?: Membership; // Optional for backward compatibility
  allOldMemberships: Membership[]; // Required for multiple memberships support
}

/**
 * Migrate component enables users to migrate their funds from an old presale contract
 * to a new presale contract in a single transaction flow.
 *
 * The component performs three sequential operations:
 * 1. Claims back funds from the old presale contract (using membership data from old contract)
 * 2. Approves the new presale contract to spend the claimed tokens
 * 3. Automatically purchases tokens from the new presale contract
 *
 * Features:
 * - Uses membership data from OLD contract (via OldMembershipsProvider)
 * - User confirmation dialog with migration details
 * - Progress indicators for each transaction step
 * - Comprehensive error handling and user feedback
 * - Automatic data refresh after successful migration
 *
 * Note: This component expects to receive membership data from the OLD contract,
 * not the new one. The OldMembershipsProvider should be used to provide this data.
 */

export function Migrate({ membership, allOldMemberships }: MigrateProps) {
  // Context hooks
  const { fetchMemberships } = useContext(MembershipsContext);
  const { presaleInstance } = useContext(PresaleContext);
  const { chain, vestedToken, collectedToken } = useContext(ProjectConfigContext);
  const { fetchCollectedTokenData } = useContext(TokensContext);
  const config = useContext(WagmiContext);
  const { publicClient } = useContext(PublicClientContext);

  // State management
  const [isLoading, setIsLoading] = useState(false);
  const [migrationStep, setMigrationStep] = useState<
    'idle' | 'claiming' | 'approving' | 'purchasing' | 'completed' | 'error'
  >('idle');
  const [showPreMigrationConfirmation, setShowPreMigrationConfirmation] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [selectedMembershipIds, setSelectedMembershipIds] = useState<string[]>([]);
  const [migrationMode, setMigrationMode] = useState<'single' | 'multiple'>('single');

  // Use provided membership or first available for backward compatibility
  const primaryMembership = membership || allOldMemberships[0];

  // Calculate total claimable amount from selected memberships
  const getSelectedMemberships = () => {
    if (migrationMode === 'single') {
      return [primaryMembership];
    }
    return allOldMemberships.filter(m => selectedMembershipIds.includes(m.id));
  };

  const selectedMemberships = getSelectedMemberships();

  // Validate selected memberships
  const membershipValidations = selectedMemberships.map(membership => ({
    membership,
    validation: validateMembership(membership),
  }));

  const validMemberships = membershipValidations.filter(mv => mv.validation.isValid);
  const invalidMemberships = membershipValidations.filter(mv => !mv.validation.isValid);
  
  // Calculate totals only from valid memberships
  const totalClaimableFromOldContract = validMemberships.reduce((total, mv) => {
    return total + (BigInt(mv.membership.usage.max) * BigInt(mv.membership.price)) / BigInt(10 ** vestedToken.decimals);
  }, 0n);

  const totalVestedTokenAmountToMigrate = validMemberships.reduce((total, mv) => {
    return total + BigInt(mv.membership.usage.max);
  }, 0n);

  // Initialize selected memberships - auto-select all valid memberships
  React.useEffect(() => {
    if (allOldMemberships.length > 0 && selectedMembershipIds.length === 0) {
      // Auto-select all memberships that have usage.max > 0
      const validMembershipIds = allOldMemberships
        .filter(membership => BigInt(membership.usage.max) > 0n)
        .map(membership => membership.id);

      if (validMembershipIds.length > 0) {
        setSelectedMembershipIds(validMembershipIds);
        // If multiple valid memberships, default to multiple mode
        if (validMembershipIds.length > 1) {
          setMigrationMode('multiple');
        }
      } else {
        // Fallback to first membership if none have valid usage
        setSelectedMembershipIds([allOldMemberships[0].id]);
      }
    }
  }, [allOldMemberships, selectedMembershipIds.length]);



  // Handle migrate all button click
  const handleMigrateAll = async () => {
    setShowPreMigrationConfirmation(true);
  };

  // Perform the actual migration
  async function performMigration() {
    // Basic validation checks
    if (totalVestedTokenAmountToMigrate === 0n) {
      setErrorMessage('No funds available to migrate.');
      setMigrationStep('error');
      return;
    }

    if (selectedMemberships.length === 0) {
      setErrorMessage('Please select at least one membership to migrate.');
      setMigrationStep('error');
      return;
    }

    // Pre-flight validation - check if any memberships have no claimable tokens
    const invalidMemberships = selectedMemberships.filter(membership => BigInt(membership.usage.max) === 0n);

    if (invalidMemberships.length > 0) {
      const invalidDetails = invalidMemberships.map(membership => {
        const roundType = detectRoundType(membership);
        return `${roundType} Membership ${membership.id}`;
      }).join(', ');

      setErrorMessage(`Migration cannot proceed. The following memberships have no claimable tokens:\n\n${invalidDetails}\n\nPlease deselect these memberships or contact support for assistance.`);
      setMigrationStep('error');
      return;
    }
    const client = await getWalletClient(config!);
    if (!client) {
      setErrorMessage('Please connect your wallet to continue.');
      setMigrationStep('error');
      return;
    }

    setIsLoading(true);
    setErrorMessage('');

    try {
      // Step 1: Claim back from old contract
      setMigrationStep('claiming');

      // Create instance of old presale contract
      let oldPresaleInstance: IPresale;
      try {
        oldPresaleInstance = await PresaleFactory.createInstance(
          OLD_PRESALE_VERSION,
          publicClient,
          OLD_PRESALE_CONTRACT_ADDRESS,
        );
        // Debug: Old presale instance created successfully
      } catch {
        throw new Error(
          'Failed to connect to old presale contract. Please check the contract address.',
        );
      }

      // Claim back tokens from old contract for each selected membership
      const claimTxHashes: string[] = [];

      // Process all selected memberships (already validated above)
      for (const membershipToMigrate of selectedMemberships) {
        const claimTxHash = await oldPresaleInstance.claimBackTokens(
          client,
          chain,
          membershipToMigrate.id,
          BigInt(membershipToMigrate.usage.max),
        );
        claimTxHashes.push(claimTxHash);

        // Wait for each claim transaction to complete
        await publicClient.waitForTransactionReceipt({
          hash: claimTxHash,
        });
      }



      // Step 2: Approve tokens for new contract
      setMigrationStep('approving');

      // Validate presale instance exists for approval
      if (!presaleInstance) {
        throw new Error(
          'New presale contract not available. Please refresh the page and try again.',
        );
      }

      // Create ERC20 contract instance for the collected token
      const collectedTokenContract = getContract({
        address: collectedToken.address,
        abi: erc20Abi,
        client: {
          public: publicClient,
          wallet: client,
        },
      });

      // Approve the new presale contract to spend the claimed tokens
      // Approve 10% more tokens to provide buffer for any calculation differences
      const approvalAmount = (totalClaimableFromOldContract * 110n) / 100n;
      const approveTxHash = await collectedTokenContract.write.approve(
        [
          presaleInstance.getPresaleData().presaleContractAddress,
          approvalAmount,
        ],
        {
          account: client.account,
          chain,
        },
      );

      // Wait for approval transaction to complete
      await publicClient.waitForTransactionReceipt({
        hash: approveTxHash,
      });

      // Step 3: Purchase from new contract
      setMigrationStep('purchasing');

      // CRITICAL FIX: Get user's NEW membership from new contract for migration
      // The old membership cannot be used with the new contract
      const userAddress = client.account?.address;
      if (!userAddress) {
        throw new Error('User address not found');
      }

      // Get fresh memberships from NEW contract for this user
      const userNewMemberships = await presaleInstance.getMemberships(userAddress);
      
      console.log('Migration Debug - User new memberships:', userNewMemberships);
      console.log('Migration Debug - Old memberships being migrated:', selectedMemberships);
      
      // Find a suitable membership for migration (prefer proof-based for first-time purchase)
      const newMembershipForMigration = userNewMemberships.find(m => 
        m.proofs && m.proofs.length > 0 // Proof-based membership for new purchase
      ) || userNewMemberships[0]; // Fallback to first available

      if (!newMembershipForMigration) {
        console.error('Migration Error - No new memberships found:', {
          userAddress,
          userNewMemberships,
          oldMemberships: selectedMemberships
        });
        throw new Error('No valid membership found in new contract for migration. User may not be whitelisted in the new contract.');
      }

      console.log('Migration Debug - Using new membership for purchase:', newMembershipForMigration);

      // Purchase tokens from new contract using NEW membership data
      const purchaseTxHash = await presaleInstance.buyTokens(
        client,
        chain,
        newMembershipForMigration, // Use NEW membership, not old one!
        totalVestedTokenAmountToMigrate,
      );

      // Wait for purchase transaction to complete
      await publicClient.waitForTransactionReceipt({
        hash: purchaseTxHash,
      });

      // Step 4: Complete migration
      setMigrationStep('completed');

      // Refresh data
      fetchCollectedTokenData();
      fetchMemberships();

      // Reset state after a short delay
      setTimeout(() => {
        setShowConfirmation(false);
        setMigrationStep('idle');
      }, 2000);
    } catch (error) {
      console.error('Migration failed:', error);
      setMigrationStep('error');

      // Determine error message based on migration step
      let userErrorMessage = 'Migration failed. Please try again.';

      if (migrationStep === 'claiming') {
        userErrorMessage =
          'Failed to claim funds from old contract. Please check your transaction and try again.';
      } else if (migrationStep === 'approving') {
        userErrorMessage =
          'Claiming succeeded but token approval failed. Please try approving manually and then purchase.';
      } else if (migrationStep === 'purchasing') {
        userErrorMessage =
          'Claiming and approval succeeded but purchase from new contract failed. Your funds may be available to claim manually.';
      }

      // Handle specific error types with graceful degradation
      if (error instanceof Error) {
        if (error.message.includes('User rejected')) {
          userErrorMessage = 'Transaction was cancelled by user.';
        } else if (error.message.includes('insufficient funds')) {
          userErrorMessage = 'Insufficient funds for gas fees.';
        } else if (error.message.includes('execution reverted')) {
          // Check if it's a claimback-specific error
          if (error.message.includes('ClaimbackNotAllowed') || migrationStep === 'claiming') {
            userErrorMessage = `Claimback failed for one or more memberships. This often happens when:\n\n`;
            userErrorMessage += `• Round state has changed since validation\n`;
            userErrorMessage += `• Membership has already been used\n`;
            userErrorMessage += `• Contract conditions have changed\n\n`;
            userErrorMessage += `NEXT STEPS:\n`;
            userErrorMessage += `• Try deselecting problematic memberships and retry\n`;
            userErrorMessage += `• Contact <EMAIL> for manual migration\n`;
            userErrorMessage += `• Check if you have other valid memberships to migrate`;
          } else {
            userErrorMessage = 'Transaction failed. Please check contract conditions and try again.';
          }
        } else if (error.message.includes('No valid memberships')) {
          userErrorMessage = `All selected memberships failed validation. Common reasons:\n\n`;
          userErrorMessage += `• Memberships have no claimable tokens (usage.max = 0)\n`;
          userErrorMessage += `• Memberships have already been used\n`;
          userErrorMessage += `• Round states have changed\n\n`;
          userErrorMessage += `Please refresh the page and try selecting different memberships, or contact support for assistance.`;
        }
      }

      setErrorMessage(userErrorMessage);

      // Reset to idle after showing error for a few seconds
      setTimeout(() => {
        setMigrationStep('idle');
        setErrorMessage('');
      }, 5000);
    } finally {
      setIsLoading(false);
    }
  }

  // Handle confirmation cancel
  const handleConfirmationCancel = () => {
    setShowConfirmation(false);
    setErrorMessage('');
    setMigrationStep('idle');
  };

  return (
    <Card className="row-span-1">
      <CardHeader>
        <CardTitle>Migrate</CardTitle>
      </CardHeader>
      <CardContent className="h-full">
        {/* Available Sales Information */}
        <div className="mb-4 p-3 bg-blue-900/20 rounded-lg border border-blue-700">
          <h4 className="text-sm font-medium text-blue-400 mb-2">
            Available Sales
          </h4>
          <p className="text-xs text-gray-300 mb-2">
            You can participate in the following sales after migration:
          </p>
          <div className="space-y-1">
            <div className="flex justify-between items-center text-xs">
              <span className="text-gray-300">• New Presale Contract (v5)</span>
              <span className="text-green-400">Active</span>
            </div>
            <div className="flex justify-between items-center text-xs">
              <span className="text-gray-400">• TGLP Rounds</span>
              <span className="text-red-400">Over</span>
            </div>
            <div className="flex justify-between items-center text-xs">
              <span className="text-gray-300">• Round 2 (FCFS)</span>
              <span className="text-green-400">Available</span>
            </div>
          </div>
          <p className="text-xs text-yellow-400 mt-2">
            Note: Participation depends on your whitelist status in the new contract
          </p>
        </div>

        {/* Pre-Migration Confirmation Dialog */}
        <Dialog
          open={showPreMigrationConfirmation}
          onOpenChange={(open) => {
            if (!open) {
              setShowPreMigrationConfirmation(false);
            }
          }}
        >
          <DialogContent className="bg-black text-white max-w-2xl">
            <DialogHeader>
              <DialogTitle>Migration Information</DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div className="p-4 bg-blue-900/20 rounded-lg border border-blue-700">
                <h4 className="text-sm font-medium text-blue-400 mb-3">
                  Migration Process
                </h4>
                <p className="text-sm text-gray-300 mb-4">
                  NOTE: Once you click migrate, you need to complete multiple transactions on
                  MetaMask/your wallet to confirm your migration to the new presale smart
                  contract.
                </p>
                <p className="text-sm text-gray-300 mb-4">
                  After migration is completed, you can connect your TGLP subscriber
                  wallet to the new presale round at{' '}
                  <a
                    href="https://presale.raiinmaker.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:text-blue-300 underline"
                  >
                    https://presale.raiinmaker.com/
                  </a>{' '}
                  to check your allocation, purchases, etc.
                </p>
              </div>

              {/* Membership Selection */}
              {allOldMemberships.length > 1 && (
                <div className="p-4 bg-purple-900/20 rounded-lg border border-purple-700">
                  <h4 className="text-sm font-medium text-purple-400 mb-3">
                    Select Memberships to Migrate
                  </h4>
                  
                  <RadioGroup 
                    value={migrationMode} 
                    onValueChange={(value: 'single' | 'multiple') => {
                      setMigrationMode(value);
                      if (value === 'single') {
                        setSelectedMembershipIds([allOldMemberships[0].id]);
                      } else {
                        setSelectedMembershipIds(allOldMemberships.map(m => m.id));
                      }
                    }}
                    className="mb-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="single" id="single" />
                      <Label htmlFor="single" className="text-sm text-gray-300">
                        Migrate single membership
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="multiple" id="multiple" />
                      <Label htmlFor="multiple" className="text-sm text-gray-300">
                        Migrate multiple memberships
                      </Label>
                    </div>
                  </RadioGroup>

                  {migrationMode === 'single' && (
                    <div className="space-y-2">
                      <p className="text-xs text-gray-400 mb-2">Select membership:</p>
                      <RadioGroup 
                        value={selectedMembershipIds[0] || ''} 
                        onValueChange={(value) => setSelectedMembershipIds([value])}
                      >
                        {allOldMemberships.map((membership) => {
                          const claimable = (BigInt(membership.usage.max) * BigInt(membership.price)) / BigInt(10 ** vestedToken.decimals);
                          return (
                            <div key={membership.id} className="flex items-center space-x-2">
                              <RadioGroupItem value={membership.id} id={membership.id} />
                              <Label htmlFor={membership.id} className="text-xs text-gray-300 flex-1">
                                <div className="flex justify-between">
                                  <span>{membership.id}</span>
                                  <span className="text-green-400">
                                    {formatUnits(claimable, collectedToken.decimals)} {collectedToken.symbol}
                                  </span>
                                </div>
                              </Label>
                            </div>
                          );
                        })}
                      </RadioGroup>
                    </div>
                  )}

                  {migrationMode === 'multiple' && (
                    <div className="space-y-2">
                      <p className="text-xs text-gray-400 mb-2">Select memberships to migrate:</p>
                      {allOldMemberships.map((membership) => {
                        const claimable = (BigInt(membership.usage.max) * BigInt(membership.price)) / BigInt(10 ** vestedToken.decimals);
                        return (
                          <div key={membership.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={membership.id}
                              checked={selectedMembershipIds.includes(membership.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedMembershipIds(prev => [...prev, membership.id]);
                                } else {
                                  setSelectedMembershipIds(prev => prev.filter(id => id !== membership.id));
                                }
                              }}
                            />
                            <Label htmlFor={membership.id} className="text-xs text-gray-300 flex-1">
                              <div className="flex justify-between">
                                <span>{membership.id}</span>
                                <span className="text-green-400">
                                  {formatUnits(claimable, collectedToken.decimals)} {collectedToken.symbol}
                                </span>
                              </div>
                            </Label>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              )}

              {/* Migration Status with Validation */}
              {invalidMemberships.length > 0 ? (
                <div className="space-y-3">
                  {/* Show validation issues */}
                  <div className="p-4 bg-yellow-900/20 rounded-lg border border-yellow-700">
                    <h4 className="text-sm font-medium text-yellow-400 mb-2">
                      Migration Issues
                    </h4>
                    <div className="space-y-2">
                      {invalidMemberships.map(mv => (
                        <div key={mv.membership.id} className="flex justify-between items-center text-xs bg-yellow-800/30 p-2 rounded">
                          <span>{mv.validation.roundType} Membership {mv.membership.id}</span>
                          <span className="text-yellow-300">{mv.validation.reason}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Show valid memberships if any */}
                  {validMemberships.length > 0 && (
                    <div className="p-4 bg-green-900/20 rounded-lg border border-green-700">
                      <h4 className="text-sm font-medium text-green-400 mb-2">
                        Valid for Migration
                      </h4>
                      <p className="text-sm text-gray-300 mb-2">
                        {validMemberships.length} membership{validMemberships.length !== 1 ? 's' : ''} can be migrated successfully.
                      </p>
                      <div className="space-y-1">
                        {validMemberships.map(mv => {
                          const claimable = (BigInt(mv.membership.usage.max) * BigInt(mv.membership.price)) / BigInt(10 ** vestedToken.decimals);
                          return (
                            <div key={mv.membership.id} className="flex justify-between items-center text-xs bg-green-800/30 p-2 rounded">
                              <span>{mv.validation.roundType} Membership {mv.membership.id}</span>
                              <span className="text-green-300">
                                {formatUnits(claimable, collectedToken.decimals)} {collectedToken.symbol}
                              </span>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </div>
              ) : totalClaimableFromOldContract > 0n ? (
                <div className="p-4 bg-green-900/20 rounded-lg border border-green-700">
                  <h4 className="text-sm font-medium text-green-400 mb-2">
                    Migration Available
                  </h4>
                  <p className="text-sm text-gray-300 mb-2">
                    Total funds available for migration: <span className="font-semibold text-white">
                      {formatUnits(totalClaimableFromOldContract, collectedToken.decimals)} {collectedToken.symbol}
                    </span>
                  </p>
                  <p className="text-sm text-gray-300">
                    Selected {selectedMemberships.length} membership{selectedMemberships.length !== 1 ? 's' : ''} for migration.
                  </p>
                </div>
              ) : (
                <div className="p-4 bg-yellow-900/20 rounded-lg border border-yellow-700">
                  <h4 className="text-sm font-medium text-yellow-400 mb-2">
                    No Migration Available
                  </h4>
                  <p className="text-sm text-gray-300">
                    No funds are available for migration from the selected memberships.
                  </p>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                variant={'outline'}
                onClick={() => setShowPreMigrationConfirmation(false)}
              >
                {totalClaimableFromOldContract > 0n ? 'Cancel' : 'Close'}
              </Button>

              {/* Show different buttons based on validation status */}
              {validMemberships.length > 0 && (
                <Button
                  variant={'default'}
                  onClick={() => {
                    setShowPreMigrationConfirmation(false);
                    setShowConfirmation(true);
                  }}
                >
                  {invalidMemberships.length > 0
                    ? `Proceed with ${validMemberships.length} Valid Membership${validMemberships.length !== 1 ? 's' : ''}`
                    : 'Proceed with Migration'
                  }
                </Button>
              )}

              {/* Show support contact for invalid memberships */}
              {invalidMemberships.length > 0 && validMemberships.length === 0 && (
                <Button
                  variant={'secondary'}
                  onClick={() => {
                    window.open('mailto:<EMAIL>?subject=Migration Assistance&body=Hello, I need assistance with migrating my memberships. Please help me with manual migration.', '_blank');
                  }}
                >
                  Contact Support for Help
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Existing Migration Confirmation Dialog */}
        <Dialog
          open={showConfirmation}
          onOpenChange={(open) => {
            if (!open) {
              setMigrationStep('idle');
              setShowConfirmation(false);
              setErrorMessage('');
            }
          }}
        >
          <Button
            variant={'secondary'}
            onClick={handleMigrateAll}
            disabled={validMemberships.length === 0 || isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Spinner className="animate-spin mr-2" />
                {migrationStep === 'claiming' && 'Claiming from old contract...'}
                {migrationStep === 'approving' && 'Approving tokens...'}
                {migrationStep === 'purchasing' && 'Purchasing from new contract...'}
                {migrationStep === 'idle' && 'Processing...'}
              </>
            ) : (
              `Migrate ${allOldMemberships.length > 1 ? 'Selected ' : ''}Funds`
            )}
          </Button>

          <DialogContent className="bg-black text-white">
            <DialogHeader>
              <DialogTitle>Confirm Migration</DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div className="p-4 bg-red-900/20 rounded-lg border border-red-700">
                <h4 className="text-sm font-medium text-red-400 mb-2">
                  Confirm Migration
                </h4>
                <p className="text-sm text-gray-300 mb-3">
                  You are about to migrate{' '}
                  <span className="font-semibold text-white">
                    {cutDecimals(
                      formatUnits(totalClaimableFromOldContract, collectedToken.decimals),
                      2,
                    )}{' '}
                    {collectedToken.symbol}
                  </span>{' '}
                  from {selectedMemberships.length} membership{selectedMemberships.length !== 1 ? 's' : ''} in the old presale contract to the new one.
                </p>
                
                {selectedMemberships.length > 1 && (
                  <div className="mb-3 p-2 bg-gray-800/50 rounded text-xs">
                    <p className="text-gray-400 mb-1">Migrating from:</p>
                    {selectedMemberships.map((m) => {
                      const claimable = (BigInt(m.usage.max) * BigInt(m.price)) / BigInt(10 ** vestedToken.decimals);
                      return (
                        <div key={m.id} className="flex justify-between text-gray-300">
                          <span>{m.id}</span>
                          <span>{formatUnits(claimable, collectedToken.decimals)} {collectedToken.symbol}</span>
                        </div>
                      );
                    })}
                  </div>
                )}
                <div className="space-y-2 text-xs text-gray-300">
                  <p>
                    <strong>Step 1:</strong> Claim back all available{' '}
                    {collectedToken.symbol} from {selectedMemberships.length} membership{selectedMemberships.length !== 1 ? 's' : ''} in old contract
                  </p>
                  <p>
                    <strong>Step 2:</strong> Approve new contract to spend claimed tokens
                  </p>
                  <p>
                    <strong>Step 3:</strong> Purchase tokens in new contract with claimed
                    funds
                  </p>
                </div>
                <p className="text-xs text-yellow-400 mt-3">
                  This action cannot be undone. Please ensure you have sufficient gas for
                  all {selectedMemberships.length + 2} transactions ({selectedMemberships.length} claim + 1 approve + 1 purchase).
                </p>
              </div>

              {migrationStep !== 'idle' && (
                <div className="p-4 bg-blue-900/20 rounded-lg border border-blue-700">
                  <h4 className="text-sm font-medium text-blue-400 mb-2">
                    Migration Progress
                  </h4>
                  <div className="space-y-2">
                    <div
                      className={`flex items-center space-x-2 ${migrationStep === 'claiming' ? 'text-yellow-400' : migrationStep === 'approving' || migrationStep === 'purchasing' || migrationStep === 'completed' ? 'text-green-400' : 'text-gray-400'}`}
                    >
                      {migrationStep === 'claiming' && (
                        <Spinner className="animate-spin w-4 h-4" />
                      )}
                      {(migrationStep === 'approving' ||
                        migrationStep === 'purchasing' ||
                        migrationStep === 'completed') && <span>✓</span>}
                      {migrationStep === 'error' && <span>✗</span>}
                      <span className="text-xs">Claiming from old contract</span>
                    </div>
                    <div
                      className={`flex items-center space-x-2 ${migrationStep === 'approving' ? 'text-yellow-400' : migrationStep === 'purchasing' || migrationStep === 'completed' ? 'text-green-400' : 'text-gray-400'}`}
                    >
                      {migrationStep === 'approving' && (
                        <Spinner className="animate-spin w-4 h-4" />
                      )}
                      {(migrationStep === 'purchasing' ||
                        migrationStep === 'completed') && <span>✓</span>}
                      {migrationStep === 'error' && <span>✗</span>}
                      <span className="text-xs">Approving tokens</span>
                    </div>
                    <div
                      className={`flex items-center space-x-2 ${migrationStep === 'purchasing' ? 'text-yellow-400' : migrationStep === 'completed' ? 'text-green-400' : 'text-gray-400'}`}
                    >
                      {migrationStep === 'purchasing' && (
                        <Spinner className="animate-spin w-4 h-4" />
                      )}
                      {migrationStep === 'completed' && <span>✓</span>}
                      {migrationStep === 'error' && <span>✗</span>}
                      <span className="text-xs">Purchasing from new contract</span>
                    </div>
                  </div>
                </div>
              )}

              {migrationStep === 'error' && errorMessage && (
                <div className="p-4 bg-red-900/20 rounded-lg border border-red-700">
                  <h4 className="text-sm font-medium text-red-400 mb-2">
                    Migration Failed
                  </h4>
                  <p className="text-sm text-gray-300">{errorMessage}</p>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button variant={'outline'} onClick={handleConfirmationCancel}>
                Cancel
              </Button>

              <Button
                variant={'destructive'}
                onClick={performMigration}
                disabled={isLoading}
                className="space-x-2"
              >
                {isLoading && <Spinner className="animate-spin" />}
                <span>
                  {migrationStep === 'claiming' && 'Claiming from old contract...'}
                  {migrationStep === 'approving' && 'Approving tokens...'}
                  {migrationStep === 'purchasing' && 'Purchasing from new contract...'}
                  {migrationStep === 'idle' && 'Confirm Migration'}
                  {migrationStep === 'completed' && 'Migration Complete'}
                  {migrationStep === 'error' && 'Try Again'}
                </span>
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
